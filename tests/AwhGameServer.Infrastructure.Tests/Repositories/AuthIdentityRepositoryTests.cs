using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Infrastructure.Persistence;
using AwhGameServer.Infrastructure.Persistence.Ef;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.UsersData;
using AwhGameServer.Infrastructure.Repositories;

namespace AwhGameServer.Infrastructure.Tests.Repositories;

public class AuthIdentityRepositoryTests : IDisposable
{
    private readonly UsersDataDbContext _context;
    private readonly AuthIdentityRepository _repository;

    public AuthIdentityRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<UsersDataDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new UsersDataDbContext(options);
        _repository = new AuthIdentityRepository(_context);
    }

    #region GetByIdAsync Tests

    [Fact(DisplayName = "GetByIdAsync с валидным идентификатором возвращает AuthIdentity")]
    public async Task GetByIdAsync_WithValidId_ReturnsAuthIdentity()
    {
        var objectId = ObjectId.GenerateNewId();
        var document = new AuthIdentityDocument
        {
            Id = objectId,
            UserId = "user-123",
            AuthMethod = "OAuth2.0_Google",
            AuthToken = "token-456"
        };

        _context.AuthIdentities.Add(document);
        await _context.SaveChangesAsync();

        var result = await _repository.GetByIdAsync(new AuthIdentityId(objectId.ToString()), CancellationToken.None);

        Assert.NotNull(result);
        Assert.Equal(objectId.ToString(), result.Id.Value);
        Assert.Equal("user-123", result.UserId.Value);
        Assert.Equal("OAuth2.0_Google", result.AuthMethod);
        Assert.Equal("token-456", result.AuthToken);
    }

    [Fact(DisplayName = "GetByIdAsync с невалидным идентификатором возвращает null")]
    public async Task GetByIdAsync_WithInvalidId_ReturnsNull()
    {
        // Act
        var result = await _repository.GetByIdAsync(new AuthIdentityId("invalid-id"), CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetByAuthMethodAndTokenAsync Tests

    [Fact(DisplayName = "GetByAuthMethodAndTokenAsync с валидными данными возвращает AuthIdentity")]
    public async Task GetByAuthMethodAndTokenAsync_WithValidData_ReturnsAuthIdentity()
    {
        var objectId = ObjectId.GenerateNewId();
        var document = new AuthIdentityDocument
        {
            Id = objectId,
            UserId = "user-123",
            AuthMethod = "OAuth2.0_Google",
            AuthToken = "token-456"
        };

        _context.AuthIdentities.Add(document);
        await _context.SaveChangesAsync();

        var result = await _repository.GetByAuthMethodAndTokenAsync("OAuth2.0_Google", "token-456");

        Assert.NotNull(result);
        Assert.Equal("OAuth2.0_Google", result.AuthMethod);
        Assert.Equal("token-456", result.AuthToken);
    }

    #endregion

    #region GetByUserIdAsync Tests

    [Fact(DisplayName = "GetByUserIdAsync с валидным UserId возвращает коллекцию AuthIdentity")]
    public async Task GetByUserIdAsync_WithValidUserId_ReturnsAuthIdentities()
    {
        // Arrange
        var userId = "user-123";
        var documents = new[]
        {
            new AuthIdentityDocument
            {
                Id = ObjectId.GenerateNewId(),
                UserId = userId,
                AuthMethod = "OAuth2.0_Google",
                AuthToken = "google-token"
            },
            new AuthIdentityDocument
            {
                Id = ObjectId.GenerateNewId(),
                UserId = userId,
                AuthMethod = "OAuth2.0_Apple",
                AuthToken = "apple-token"
            }
        };

        _context.AuthIdentities.AddRange(documents);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetByUserIdAsync(new UserId(userId));

        // Assert
        Assert.Equal(2, result.Count);
        Assert.All(result, identity => Assert.Equal(userId, identity.UserId.Value));
    }

    #endregion

    #region AddAsync Tests

    [Fact(DisplayName = "AddAsync с валидным AuthIdentity добавляет в контекст")]
    public async Task AddAsync_WithValidAuthIdentity_AddsToContext()
    {
        // Arrange
        var authIdentity = new AuthIdentity(
            new AuthIdentityId(ObjectId.GenerateNewId().ToString()),
            new UserId("user-123"),
            "token-456",
            "OAuth2.0_Google"
        );

        // Act
        await _repository.AddAsync(authIdentity);
        await _context.SaveChangesAsync();

        // Assert
        var document = await _context.AuthIdentities.FirstOrDefaultAsync();
        Assert.NotNull(document);
        Assert.Equal("user-123", document.UserId);
        Assert.Equal("OAuth2.0_Google", document.AuthMethod);
        Assert.Equal("token-456", document.AuthToken);
    }

    #endregion

    public void Dispose()
    {
        _context.Dispose();
    }
}
