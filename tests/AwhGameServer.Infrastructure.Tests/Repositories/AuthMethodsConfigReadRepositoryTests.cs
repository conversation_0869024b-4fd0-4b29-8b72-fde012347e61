using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using AwhGameServer.Infrastructure.Persistence;
using AwhGameServer.Infrastructure.Persistence.Ef;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.GameData;
using AwhGameServer.Infrastructure.Repositories;

namespace AwhGameServer.Infrastructure.Tests.Repositories;

public class AuthMethodsConfigReadRepositoryTests : IDisposable
{
    private readonly GameDataDbContext _context;
    private readonly AuthMethodsConfigReadRepository _repository;

    public AuthMethodsConfigReadRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<GameDataDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new GameDataDbContext(options);
        _repository = new AuthMethodsConfigReadRepository(_context);
    }

    #region GetConfigAsync Tests

    [Fact(DisplayName = "GetConfigAsync с методами аутентификации возвращает AuthMethodsConfig")]
    public async Task GetConfigAsync_WithAuthMethods_ReturnsAuthMethodsConfig()
    {
        // Arrange
        var documents = new[]
        {
            new AuthMethodDocument
            {
                Id = ObjectId.GenerateNewId(),
                MethodKey = "OAuth2.0_Google",
                IsRegistrationAllowed = true,
                IsLoginAllowed = true
            },
            new AuthMethodDocument
            {
                Id = ObjectId.GenerateNewId(),
                MethodKey = "OAuth2.0_Apple",
                IsRegistrationAllowed = false,
                IsLoginAllowed = true
            },
            new AuthMethodDocument
            {
                Id = ObjectId.GenerateNewId(),
                MethodKey = "Guest",
                IsRegistrationAllowed = true,
                IsLoginAllowed = false
            }
        };

        _context.AuthMethods.AddRange(documents);
        await _context.SaveChangesAsync();

        // Act
        var result = await _repository.GetConfigAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.AllowedAuthMethods.Count);

        var googleMethod = result.AllowedAuthMethods.First(m => m.MethodKey == "OAuth2.0_Google");
        Assert.True(googleMethod.IsRegistrationAllowed);
        Assert.True(googleMethod.IsLoginAllowed);

        var appleMethod = result.AllowedAuthMethods.First(m => m.MethodKey == "OAuth2.0_Apple");
        Assert.False(appleMethod.IsRegistrationAllowed);
        Assert.True(appleMethod.IsLoginAllowed);

        var guestMethod = result.AllowedAuthMethods.First(m => m.MethodKey == "Guest");
        Assert.False(guestMethod.IsRegistrationAllowed);
        Assert.False(guestMethod.IsLoginAllowed);
    }

    [Fact(DisplayName = "GetConfigAsync без методов аутентификации возвращает пустую конфигурацию")]
    public async Task GetConfigAsync_WithNoAuthMethods_ReturnsEmptyConfig()
    {
        // Act
        var result = await _repository.GetConfigAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result.AllowedAuthMethods);
    }

    #endregion

    public void Dispose()
    {
        _context.Dispose();
    }
}
