using MongoDB.Bson;
using AwhGameServer.Domain.ValueObjects;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Infrastructure.Services;

namespace AwhGameServer.Infrastructure.Tests.Services;

public sealed record TestAuthIdentityId(string Value) : TypedId(Value);
public sealed record TestCustomId(string Value) : TypedId(Value);

public class MongoObjectIdGeneratorTests
{
    [Fact(DisplayName = "New генерирует валидный MongoDB ObjectId")]
    public async Task New_GeneratesValidMongoObjectId()
    {
        var generator = new MongoObjectIdGenerator<AuthIdentityId>();

        var result = await generator.New();

        Assert.NotNull(result);
        Assert.NotNull(result.Value);
        Assert.NotEmpty(result.Value);
        Assert.True(ObjectId.TryParse(result.Value, out _));
    }

    [Fact(DisplayName = "New возвращает корректный тип AuthIdentityId")]
    public async Task New_ReturnsCorrectAuthIdentityIdType()
    {
        var generator = new MongoObjectIdGenerator<AuthIdentityId>();

        var result = await generator.New();

        Assert.IsType<AuthIdentityId>(result);
        Assert.Equal(24, result.Value.Length);
    }

    [Fact(DisplayName = "New генерирует ObjectId в правильном формате")]
    public async Task New_GeneratesObjectIdInCorrectFormat()
    {
        var generator = new MongoObjectIdGenerator<TestAuthIdentityId>();

        var result = await generator.New();

        Assert.Matches("^[0-9a-f]{24}$", result.Value);
    }

    [Fact(DisplayName = "New генерирует уникальные идентификаторы при множественных вызовах")]
    public async Task New_GeneratesUniqueIds_OnMultipleCalls()
    {
        var generator = new MongoObjectIdGenerator<AuthIdentityId>();

        var id1 = await generator.New();
        var id2 = await generator.New();
        var id3 = await generator.New();

        Assert.NotEqual(id1.Value, id2.Value);
        Assert.NotEqual(id1.Value, id3.Value);
        Assert.NotEqual(id2.Value, id3.Value);
    }

    [Fact(DisplayName = "New работает с различными типами TypedId")]
    public async Task New_WorksWithDifferentTypedIdTypes()
    {
        var authIdGenerator = new MongoObjectIdGenerator<AuthIdentityId>();
        var customIdGenerator = new MongoObjectIdGenerator<TestCustomId>();

        var authId = await authIdGenerator.New();
        var customId = await customIdGenerator.New();

        Assert.IsType<AuthIdentityId>(authId);
        Assert.IsType<TestCustomId>(customId);
        Assert.True(ObjectId.TryParse(authId.Value, out _));
        Assert.True(ObjectId.TryParse(customId.Value, out _));
    }

    [Fact(DisplayName = "New генерирует валидные ObjectId с корректным временем создания")]
    public async Task New_GeneratesValidObjectIdWithCorrectCreationTime()
    {
        var generator = new MongoObjectIdGenerator<TestAuthIdentityId>();
        var beforeGeneration = DateTime.UtcNow.AddSeconds(-1);

        var result = await generator.New();

        var afterGeneration = DateTime.UtcNow.AddSeconds(1);
        var objectId = ObjectId.Parse(result.Value);
        var creationTime = objectId.CreationTime;

        Assert.True(creationTime >= beforeGeneration);
        Assert.True(creationTime <= afterGeneration);
    }

    [Fact(DisplayName = "New возвращает завершенную задачу")]
    public async Task New_ReturnsCompletedTask()
    {
        var generator = new MongoObjectIdGenerator<AuthIdentityId>();

        var task = generator.New();

        Assert.True(task.IsCompleted);
        var result = await task;
        Assert.NotNull(result);
    }

    [Fact(DisplayName = "New генерирует уникальные ID при параллельных вызовах")]
    public async Task New_GeneratesUniqueIds_OnConcurrentCalls()
    {
        var generator = new MongoObjectIdGenerator<TestAuthIdentityId>();
        const int concurrentCallsCount = 100;
        var tasks = new List<Task<TestAuthIdentityId>>();

        for (int i = 0; i < concurrentCallsCount; i++)
        {
            tasks.Add(generator.New());
        }

        var results = await Task.WhenAll(tasks);
        var uniqueIds = results.Select(x => x.Value).Distinct().ToList();

        Assert.Equal(concurrentCallsCount, uniqueIds.Count);
        Assert.All(uniqueIds, id => Assert.True(ObjectId.TryParse(id, out _)));
    }

    [Fact(DisplayName = "New создает TypedId через Activator корректно")]
    public async Task New_CreatesTypedIdThroughActivatorCorrectly()
    {
        var generator = new MongoObjectIdGenerator<TestCustomId>();

        var result = await generator.New();

        Assert.NotNull(result);
        Assert.IsType<TestCustomId>(result);
        Assert.NotNull(result.Value);
        Assert.NotEmpty(result.Value);
    }

    [Theory(DisplayName = "New работает с различными реализациями TypedId")]
    [InlineData(typeof(AuthIdentityId))]
    [InlineData(typeof(TestAuthIdentityId))]
    [InlineData(typeof(TestCustomId))]
    public async Task New_WorksWithVariousTypedIdImplementations(Type typedIdType)
    {
        var generatorType = typeof(MongoObjectIdGenerator<>).MakeGenericType(typedIdType);
        var generator = Activator.CreateInstance(generatorType);
        var newMethod = generatorType.GetMethod("New");

        var task = (Task)newMethod!.Invoke(generator, null)!;
        await task;

        var result = task.GetType().GetProperty("Result")!.GetValue(task);
        var typedId = (TypedId)result!;

        Assert.NotNull(typedId);
        Assert.IsType(typedIdType, typedId);
        Assert.True(ObjectId.TryParse(typedId.Value, out _));
    }

    [Fact(DisplayName = "New генерирует последовательные ObjectId с возрастающими значениями")]
    public async Task New_GeneratesSequentialObjectIds()
    {
        var generator = new MongoObjectIdGenerator<TestAuthIdentityId>();

        var id1 = await generator.New();
        await Task.Delay(1);
        var id2 = await generator.New();

        var objectId1 = ObjectId.Parse(id1.Value);
        var objectId2 = ObjectId.Parse(id2.Value);

        Assert.True(objectId2.CreationTime >= objectId1.CreationTime);
    }

    [Fact(DisplayName = "New генерирует ObjectId с корректной структурой")]
    public async Task New_GeneratesObjectIdWithCorrectStructure()
    {
        var generator = new MongoObjectIdGenerator<AuthIdentityId>();

        var result = await generator.New();

        var objectId = ObjectId.Parse(result.Value);
        Assert.True(objectId.Timestamp > 0);
        Assert.Equal(24, result.Value.Length);
        Assert.True(result.Value.All(c => "0123456789abcdef".Contains(char.ToLower(c))));
    }

    [Fact(DisplayName = "New работает стабильно при большом количестве вызовов")]
    public async Task New_WorksStablyWithLargeNumberOfCalls()
    {
        var generator = new MongoObjectIdGenerator<TestCustomId>();
        const int largeCallsCount = 1000;
        var results = new List<TestCustomId>();

        for (int i = 0; i < largeCallsCount; i++)
        {
            var result = await generator.New();
            results.Add(result);
        }

        var uniqueIds = results.Select(x => x.Value).Distinct().ToList();
        Assert.Equal(largeCallsCount, uniqueIds.Count);
        Assert.All(results, result => Assert.True(ObjectId.TryParse(result.Value, out _)));
    }
}
