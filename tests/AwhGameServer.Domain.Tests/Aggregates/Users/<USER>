using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Domain.Exceptions;

namespace AwhGameServer.Domain.Tests.Aggregates.Users;

public class AuthIdentityTests
{
    #region Constructor Tests

    [Fact(DisplayName = "Создание AuthIdentity с валидными параметрами успешно")]
    public void Constructor_WithValidParameters_CreatesSuccessfully()
    {
        // Arrange
        var authIdentityId = new AuthIdentityId("auth-identity-123");
        var userId = new UserId("user-123");
        const string authToken = "token-456";
        const string authMethod = "OAuth2.0_Google";

        // Act
        var authIdentity = new AuthIdentity(authIdentityId, userId, authToken, authMethod);

        // Assert
        Assert.Equal(authIdentityId, authIdentity.Id);
        Assert.Equal(userId, authIdentity.UserId);
        Assert.Equal(authToken, authIdentity.AuthToken);
        Assert.Equal(authMethod, authIdentity.AuthMethod);
    }

    [Theory(DisplayName = "Создание AuthIdentity с null или пустым authToken вызывает DomainException")]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    [InlineData("\r\n")]
    [InlineData(" \t \n ")]
    public void Constructor_WithNullOrEmptyAuthToken_ThrowsDomainException(string invalidAuthToken)
    {
        // Arrange
        var authIdentityId = new AuthIdentityId("auth-identity-123");
        var userId = new UserId("user-123");
        const string authMethod = "OAuth2.0_Google";

        // Act & Assert
        var exception = Assert.Throws<DomainException>(() =>
            new AuthIdentity(authIdentityId, userId, invalidAuthToken, authMethod));
        Assert.Equal("Auth token cannot be null or empty", exception.Message);
    }

    [Theory(DisplayName = "Создание AuthIdentity с null или пустым authMethod вызывает DomainException")]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    [InlineData("\r\n")]
    [InlineData(" \t \n ")]
    public void Constructor_WithNullOrEmptyAuthMethod_ThrowsDomainException(string invalidAuthMethod)
    {
        // Arrange
        var authIdentityId = new AuthIdentityId("auth-identity-123");
        var userId = new UserId("user-123");
        const string authToken = "token-456";

        // Act & Assert
        var exception = Assert.Throws<DomainException>(() =>
            new AuthIdentity(authIdentityId, userId, authToken, invalidAuthMethod));
        Assert.Equal("Auth method cannot be null or empty", exception.Message);
    }

    [Theory(DisplayName = "Создание AuthIdentity с различными валидными значениями")]
    [InlineData("user-1", "token-1", "OAuth2.0_Google")]
    [InlineData("user-550e8400-e29b-41d4-a716-446655440000", "google-token-123456", "OAuth2.0_Google")]
    [InlineData("guest-user", "apple-id-token", "OAuth2.0_Apple")]
    [InlineData("phone-user", "+1234567890", "PhoneNumber")]
    [InlineData("email-user", "<EMAIL>", "EmailPassword")]
    [InlineData("vk-user", "vk-token-789", "OAuth2.0_VK")]
    [InlineData("custom-user", "custom-token", "Custom_Method_123")]
    public void Constructor_WithValidValues_PreservesValues(string userIdValue, string authToken, string authMethod)
    {
        // Arrange
        var authIdentityId = new AuthIdentityId($"auth-{userIdValue}");
        var userId = new UserId(userIdValue);

        // Act
        var authIdentity = new AuthIdentity(authIdentityId, userId, authToken, authMethod);

        // Assert
        Assert.Equal(authIdentityId, authIdentity.Id);
        Assert.Equal(userId, authIdentity.UserId);
        Assert.Equal(userIdValue, authIdentity.UserId.Value);
        Assert.Equal(authToken, authIdentity.AuthToken);
        Assert.Equal(authMethod, authIdentity.AuthMethod);
    }



    [Fact(DisplayName = "Свойство Id корректно возвращает переданный AuthIdentityId")]
    public void Id_Property_ReturnsCorrectAuthIdentityId()
    {
        // Arrange
        var authIdentityId = new AuthIdentityId("unique-auth-id-456");
        var userId = new UserId("user-123");
        const string authToken = "token-456";
        const string authMethod = "OAuth2.0_Google";

        // Act
        var authIdentity = new AuthIdentity(authIdentityId, userId, authToken, authMethod);

        // Assert
        Assert.Equal(authIdentityId, authIdentity.Id);
        Assert.Equal("unique-auth-id-456", authIdentity.Id.Value);
        Assert.Equal("unique-auth-id-456", authIdentity.Id.ToString());
    }

    [Fact(DisplayName = "Создание AuthIdentity с null AuthIdentityId вызывает DomainException")]
    public void Constructor_WithNullAuthIdentityId_ThrowsDomainException()
    {
        // Arrange
        AuthIdentityId authIdentityId = null;
        var userId = new UserId("user-123");
        const string authToken = "token-456";
        const string authMethod = "OAuth2.0_Google";

        // Act & Assert
        var exception = Assert.Throws<DomainException>(() =>
            new AuthIdentity(authIdentityId, userId, authToken, authMethod));
        Assert.Equal("Id cannot be null", exception.Message);
    }

    [Fact(DisplayName = "Создание AuthIdentity с null UserId вызывает DomainException")]
    public void Constructor_WithNullUserId_ThrowsDomainException()
    {
        // Arrange
        var authIdentityId = new AuthIdentityId("auth-identity-123");
        UserId userId = null;
        const string authToken = "token-456";
        const string authMethod = "OAuth2.0_Google";

        // Act & Assert
        var exception = Assert.Throws<DomainException>(() =>
            new AuthIdentity(authIdentityId, userId, authToken, authMethod));
        Assert.Equal("UserId cannot be null", exception.Message);
    }

    #endregion

    #region Edge Cases

    [Fact(DisplayName = "AuthMethod чувствителен к регистру")]
    public void AuthMethod_IsCaseSensitive()
    {
        // Arrange
        var authIdentityId1 = new AuthIdentityId("auth-identity-1");
        var authIdentityId2 = new AuthIdentityId("auth-identity-2");
        var authIdentityId3 = new AuthIdentityId("auth-identity-3");
        var userId = new UserId("user-123");
        const string authToken = "token-456";

        // Act
        var authIdentityLower = new AuthIdentity(authIdentityId1, userId, authToken, "oauth2.0_google");
        var authIdentityUpper = new AuthIdentity(authIdentityId2, userId, authToken, "OAUTH2.0_GOOGLE");
        var authIdentityMixed = new AuthIdentity(authIdentityId3, userId, authToken, "OAuth2.0_Google");

        // Assert
        Assert.Equal("oauth2.0_google", authIdentityLower.AuthMethod);
        Assert.Equal("OAUTH2.0_GOOGLE", authIdentityUpper.AuthMethod);
        Assert.Equal("OAuth2.0_Google", authIdentityMixed.AuthMethod);

        Assert.NotEqual(authIdentityLower.AuthMethod, authIdentityUpper.AuthMethod);
        Assert.NotEqual(authIdentityLower.AuthMethod, authIdentityMixed.AuthMethod);
        Assert.NotEqual(authIdentityUpper.AuthMethod, authIdentityMixed.AuthMethod);
    }

    [Fact(DisplayName = "AuthToken может содержать специальные символы")]
    public void AuthToken_CanContainSpecialCharacters()
    {
        // Arrange
        var authIdentityId = new AuthIdentityId("auth-identity-123");
        var userId = new UserId("user-123");
        const string specialToken = "!@#$%^&*()_+-=[]{}|;:,.<>?/~`";
        const string authMethod = "OAuth2.0_Google";

        // Act
        var authIdentity = new AuthIdentity(authIdentityId, userId, specialToken, authMethod);

        // Assert
        Assert.Equal(specialToken, authIdentity.AuthToken);
    }

    [Fact(DisplayName = "AuthToken может быть очень длинным")]
    public void AuthToken_CanBeLong()
    {
        // Arrange
        var authIdentityId = new AuthIdentityId("auth-identity-123");
        var userId = new UserId("user-123");
        var longToken = new string('a', 1000);
        const string authMethod = "OAuth2.0_Google";

        // Act
        var authIdentity = new AuthIdentity(authIdentityId, userId, longToken, authMethod);

        // Assert
        Assert.Equal(longToken, authIdentity.AuthToken);
        Assert.Equal(1000, authIdentity.AuthToken.Length);
    }

    #endregion

    #region Integration Tests

    [Fact(DisplayName = "Полный жизненный цикл AuthIdentity работает корректно")]
    public void Integration_FullLifeCycle_WorksCorrectly()
    {
        // Arrange
        const string authIdentityIdValue = "auth-550e8400-e29b-41d4-a716-446655440000";
        const string userIdValue = "user-550e8400-e29b-41d4-a716-446655440000";
        const string authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
        const string authMethod = "OAuth2.0_Google";

        // Act - создание AuthIdentityId и UserId
        var authIdentityId = new AuthIdentityId(authIdentityIdValue);
        var userId = new UserId(userIdValue);

        // Act - создание AuthIdentity
        var authIdentity = new AuthIdentity(authIdentityId, userId, authToken, authMethod);

        // Assert - проверка всех свойств
        Assert.Equal(authIdentityId, authIdentity.Id);
        Assert.Equal(authIdentityIdValue, authIdentity.Id.Value);
        Assert.Equal(userId, authIdentity.UserId);
        Assert.Equal(userIdValue, authIdentity.UserId.Value);
        Assert.Equal(authToken, authIdentity.AuthToken);
        Assert.Equal(authMethod, authIdentity.AuthMethod);

        // Assert - проверка неизменности
        Assert.Equal(authIdentityIdValue, authIdentity.Id.ToString());
        Assert.Equal(userIdValue, authIdentity.UserId.ToString());
    }

    #endregion
}
