using System.Reflection;
using System.Runtime.CompilerServices;
using FluentAssertions;
using FluentAssertions.Execution;

namespace AwhGameServer.Domain.Tests.DomainConventions;

public class DomainConventionPerTypeTests
{
    [Theory(DisplayName = "Публичные свойства допускают только private/protected/init сеттеры")]
    [MemberData(nameof(DomainTypeData.Cases), MemberType = typeof(DomainTypeData))]
    [Trait("Layer", "Domain")]
    [Trait("Convention", "NoPublicOrInternalSetters")]
    public void Type_ShouldAllowOnlyPrivateProtectedOrInitSetters(TypeCase @case)
    {
        var type = @case.Type;

        var props = type.GetProperties(BindingFlags.Instance | BindingFlags.Public)
            .Where(p => p.GetIndexParameters().Length == 0)
            .ToArray();

        using (new AssertionScope(type.FullName))
        {
            foreach (var p in props)
            {
                p.CanRead.Should().BeTrue($"{type.FullName}.{p.Name} должен иметь публичный getter");

                SetterIsAllowed(p).Should().BeTrue(
                    $"{type.FullName}.{p.Name} имеет недопустимый setter = {DescribeSetter(p)}; " +
                    "разрешены только private/protected/private protected/init (или отсутствие сеттера)");
            }
        }

        return;

        bool SetterIsAllowed(PropertyInfo p)
        {
            var set = p.SetMethod;
            if (set is null) return true; // нет сеттера — ОК
            if (IsInitOnly(p)) return true; // init — ОК
            if (set.IsPrivate) return true; // private — ОК
            if (set.IsFamily) return true; // protected — ОК
            if (set.IsFamilyAndAssembly) return true; // private protected — ОК
            return false; // запрещено: public, internal, protected internal
        }

        bool IsInitOnly(PropertyInfo property)
        {
            var set = property.SetMethod;
            if (set is null) return false;
            var mods = set.ReturnParameter.GetRequiredCustomModifiers();
            return mods.Contains(typeof(IsExternalInit));
        }

        string DescribeSetter(PropertyInfo p)
        {
            var set = p.SetMethod;
            if (set is null) return "no setter";
            if (IsInitOnly(p)) return "init";
            if (set.IsPublic) return "public";
            if (set.IsPrivate) return "private";
            if (set.IsFamily) return "protected";
            if (set.IsFamilyAndAssembly) return "private protected";
            if (set.IsAssembly) return "internal";
            if (set.IsFamilyOrAssembly) return "protected internal";
            return "unknown";
        }
    }
}
