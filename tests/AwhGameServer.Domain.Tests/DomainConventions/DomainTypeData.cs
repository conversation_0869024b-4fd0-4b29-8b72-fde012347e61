using System.Reflection;
using System.Runtime.CompilerServices;

namespace AwhGameServer.Domain.Tests.DomainConventions;

public static class DomainTypeData
{
    private static readonly string[] AllowedNamespaceRoots =
    [
        "AwhGameServer.Domain.Aggregates",
        "AwhGameServer.Domain.Entities",
        "AwhGameServer.Domain.ValueObjects"
    ];

    private static readonly Assembly DomainAssembly =
        typeof(AwhGameServer.Domain.ValueObjects.TypedId).Assembly;

    public static IEnumerable<object[]> Cases()
    {
        foreach (var t in DomainAssembly.GetTypes())
        {
            if (t.Namespace is null) continue;
            if (!IsInAllowedNamespaces(t.Namespace)) continue;
            if (!IsRealDomainType(t)) continue;

            yield return [new TypeCase(t, PrettyName(t))];
        }
    }

    private static bool IsInAllowedNamespaces(string ns)
        => AllowedNamespaceRoots.Any(root => NamespaceStartsWith(ns, root));

    private static bool NamespaceStartsWith(string ns, string root)
    {
        var nsSeg = ns.Split('.');
        var rootSeg = root.Split('.');
        if (nsSeg.Length < rootSeg.Length) return false;
        for (int i = 0; i < rootSeg.Length; i++)
            if (!string.Equals(nsSeg[i], rootSeg[i], StringComparison.Ordinal))
                return false;
        return true;
    }

    // Фильтр «реальных» доменных типов
    private static bool IsRealDomainType(Type t)
    {
        // исключаем интерфейсы/абстрактные и нерелевантные value types
        if (t.IsInterface || t.IsAbstract) return false;
        if (!(t.IsClass || (t.IsValueType && !t.IsPrimitive && !t.IsEnum))) return false;

        // исключаем всё компилер-сгенерированное
        if (t.IsDefined(typeof(CompilerGeneratedAttribute), inherit: false)) return false;

        // отрезаем типичные паттерны (подстраховка)
        var n = t.Name;
        if (n.StartsWith("<>", StringComparison.Ordinal)) return false;          // <>c, анонимки
        if (n.Contains("DisplayClass")) return false;                            // <>c__DisplayClass...
        if (n.Contains("AnonymousType")) return false;                           // анонимные типы
        if (n.Contains("d__")) return false;                                     // state machines

        // Не берём вложенные (обычно все доменные типы top-level)
        if (t.IsNested && n.StartsWith("<", StringComparison.Ordinal)) return false;

        return true;
    }
    
    private static string PrettyName(Type t)
    {
        var ns = t.Namespace ?? "";
        var root = AllowedNamespaceRoots.FirstOrDefault(r => NamespaceStartsWith(ns, r));
        var tailNs = root is null ? ns : ns[root.Length..].TrimStart('.');
        var typeName = GetFriendlyTypeName(t);
        return string.IsNullOrEmpty(tailNs) ? typeName : $"{tailNs}.{typeName}";
    }

    private static string GetFriendlyTypeName(Type type)
    {
        if (!type.IsGenericType) return type.Name;
        var name = type.Name[..type.Name.IndexOf('`')];
        var args = string.Join(", ", type.GetGenericArguments().Select(GetFriendlyTypeName));
        return $"{name}<{args}>";
    }
}

public sealed record TypeCase(Type Type, string Name)
{
    public override string ToString() => Name;
}
