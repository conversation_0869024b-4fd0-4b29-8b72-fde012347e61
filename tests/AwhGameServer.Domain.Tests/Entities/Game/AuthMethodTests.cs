using AwhGameServer.Domain.Entities.Game;
using AwhGameServer.Domain.Exceptions;

namespace AwhGameServer.Domain.Tests.Entities.Game;

public class AuthMethodTests
{
    [Fact(DisplayName = "Создание AuthMethod с валидными параметрами успешно")]
    public void Constructor_WithValidParameters_CreatesSuccessfully()
    {
        // Arrange
        const string methodKey = "OAuth2.0_Google";
        const bool isRegistrationAllowed = true;
        const bool isLoginAllowed = true;

        // Act
        var authMethod = new AuthMethod(methodKey, isRegistrationAllowed, isLoginAllowed);

        // Assert
        Assert.Equal(methodKey, authMethod.MethodKey);
        Assert.Equal(isLoginAllowed, authMethod.IsLoginAllowed);
        Assert.Equal(isRegistrationAllowed, authMethod.IsRegistrationAllowed);
    }

    [Theory(DisplayName = "Создание AuthMethod с null или пустой строкой вызывает DomainException")]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    [InlineData("\t")]
    [InlineData("\n")]
    public void Constructor_WithNullOrEmptyMethodKey_ThrowsDomainException(string invalidMethodKey)
    {
        // Act & Assert
        var exception = Assert.Throws<DomainException>(() => 
            new AuthMethod(invalidMethodKey, true, true));
        
        Assert.Equal("Method key cannot be null or empty", exception.Message);
    }

    [Fact(DisplayName = "Когда вход запрещён, регистрация также запрещается независимо от параметра")]
    public void IsRegistrationAllowed_WhenLoginDisabled_ReturnsFalseRegardlessOfParameter()
    {
        // Arrange & Act
        var authMethodWithRegistrationTrue = new AuthMethod("Test", true, false);
        var authMethodWithRegistrationFalse = new AuthMethod("Test", false, false);

        // Assert
        Assert.False(authMethodWithRegistrationTrue.IsRegistrationAllowed);
        Assert.False(authMethodWithRegistrationFalse.IsRegistrationAllowed);
    }

    [Theory(DisplayName = "Когда вход разрешён, регистрация зависит от переданного параметра")]
    [InlineData(true, true)]
    [InlineData(false, false)]
    public void IsRegistrationAllowed_WhenLoginEnabled_ReturnsParameterValue(
        bool registrationParameter, bool expectedResult)
    {
        // Arrange & Act
        var authMethod = new AuthMethod("Test", registrationParameter, true);

        // Assert
        Assert.Equal(expectedResult, authMethod.IsRegistrationAllowed);
    }

    [Theory(DisplayName = "MethodKey сохраняется без изменений для различных валидных значений")]
    [InlineData("OAuth2.0_Google")]
    [InlineData("OAuth2.0_Apple")]
    [InlineData("OAuth2.0_VK")]
    [InlineData("PhoneNumber")]
    [InlineData("EmailPassword")]
    [InlineData("Custom_Method_123")]
    [InlineData("a")]
    [InlineData("A")]
    public void MethodKey_WithValidValues_PreservesOriginalValue(string methodKey)
    {
        // Act
        var authMethod = new AuthMethod(methodKey, true, true);

        // Assert
        Assert.Equal(methodKey, authMethod.MethodKey);
    }

    [Theory(DisplayName = "IsLoginAllowed сохраняет значение переданного параметра")]
    [InlineData(true)]
    [InlineData(false)]
    public void IsLoginAllowed_ReturnsParameterValue(bool loginAllowed)
    {
        // Act
        var authMethod = new AuthMethod("Test", true, loginAllowed);

        // Assert
        Assert.Equal(loginAllowed, authMethod.IsLoginAllowed);
    }

    [Fact(DisplayName = "MethodKey чувствителен к регистру")]
    public void MethodKey_IsCaseSensitive()
    {
        // Arrange & Act
        var authMethodLower = new AuthMethod("oauth2.0_google", true, true);
        var authMethodUpper = new AuthMethod("OAUTH2.0_GOOGLE", true, true);
        var authMethodMixed = new AuthMethod("OAuth2.0_Google", true, true);

        // Assert
        Assert.Equal("oauth2.0_google", authMethodLower.MethodKey);
        Assert.Equal("OAUTH2.0_GOOGLE", authMethodUpper.MethodKey);
        Assert.Equal("OAuth2.0_Google", authMethodMixed.MethodKey);
        
        Assert.NotEqual(authMethodLower.MethodKey, authMethodUpper.MethodKey);
        Assert.NotEqual(authMethodLower.MethodKey, authMethodMixed.MethodKey);
        Assert.NotEqual(authMethodUpper.MethodKey, authMethodMixed.MethodKey);
    }

    [Theory(DisplayName = "Все возможные комбинации параметров isRegistrationAllowed и isLoginAllowed")]
    [InlineData(true, true, true)] // registration=true, login=true -> registration should be true
    [InlineData(false, true, false)] // registration=false, login=true -> registration should be false
    [InlineData(true, false, false)] // registration=true, login=false -> registration should be false
    [InlineData(false, false, false)] // registration=false, login=false -> registration should be false
    public void Constructor_AllParameterCombinations_WorksCorrectly(
        bool registrationParam, bool loginParam, bool expectedRegistration)
    {
        // Act
        var authMethod = new AuthMethod("Test", registrationParam, loginParam);

        // Assert
        Assert.Equal(loginParam, authMethod.IsLoginAllowed);
        Assert.Equal(expectedRegistration, authMethod.IsRegistrationAllowed);
    }

    [Fact(DisplayName = "Создание объекта с минимальным валидным ключом")]
    public void Constructor_WithMinimalValidKey_WorksCorrectly()
    {
        // Arrange
        const string minimalKey = "A";

        // Act
        var authMethod = new AuthMethod(minimalKey, false, false);

        // Assert
        Assert.Equal(minimalKey, authMethod.MethodKey);
        Assert.False(authMethod.IsLoginAllowed);
        Assert.False(authMethod.IsRegistrationAllowed);
    }

    [Fact(DisplayName = "Создание объекта с длинным ключом")]
    public void Constructor_WithLongKey_WorksCorrectly()
    {
        // Arrange
        const string longKey = "VeryLongAuthenticationMethodKeyWithManyCharacters_OAuth2.0_CustomProvider_WithAdditionalParameters_2024";

        // Act
        var authMethod = new AuthMethod(longKey, true, true);

        // Assert
        Assert.Equal(longKey, authMethod.MethodKey);
        Assert.True(authMethod.IsLoginAllowed);
        Assert.True(authMethod.IsRegistrationAllowed);
    }
}
