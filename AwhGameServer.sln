
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.WebApi", "src\AwhGameServer.WebApi\AwhGameServer.WebApi.csproj", "{3EC303ED-F0F0-4AFF-8A3E-48B22F831CB3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{D1000AF1-6C0D-41EB-AD67-EF631B739FF5}"
	ProjectSection(SolutionItems) = preProject
		compose.yaml = compose.yaml
		README.md = README.md
		.gitignore = .gitignore
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.Contracts", "src\AwhGameServer.Contracts\AwhGameServer.Contracts.csproj", "{760B0CA2-7F44-4709-A738-5B89F454DD72}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.Application", "src\AwhGameServer.Application\AwhGameServer.Application.csproj", "{99A3D63F-2CDA-49FA-A1C5-027DA8090C00}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{5CA3D57C-C4FC-425A-BFE8-E6E74504FEC9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{9759C78D-2BAB-4EA7-ACC4-FA6C3A82BB46}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.Domain", "src\AwhGameServer.Domain\AwhGameServer.Domain.csproj", "{6E628078-8FFB-4AF7-955C-64F829F860D1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.Infrastructure", "src\AwhGameServer.Infrastructure\AwhGameServer.Infrastructure.csproj", "{0C79BA1D-D547-4A7B-8B8F-557FBF068E9F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.Domain.Tests", "tests\AwhGameServer.Domain.Tests\AwhGameServer.Domain.Tests.csproj", "{505B6DB0-7D95-4927-8ED8-87160701B1E3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.Application.Tests", "tests\AwhGameServer.Application.Tests\AwhGameServer.Application.Tests.csproj", "{4FB842DF-6A36-4A49-B06D-A051C0FA0AEA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.Infrastructure.Tests", "tests\AwhGameServer.Infrastructure.Tests\AwhGameServer.Infrastructure.Tests.csproj", "{791A8AA0-1457-482B-BB2B-9B12245BD4C5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AwhGameServer.WebApi.Tests", "tests\AwhGameServer.WebApi.Tests\AwhGameServer.WebApi.Tests.csproj", "{72901E8E-6B98-4B0B-8C04-9FAD4788C4AB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{B99B9D8A-D8FA-448E-8746-1A0D6A7F23D8}"
	ProjectSection(SolutionItems) = preProject
		docs\Authentication.http = docs\Authentication.http
		docs\http-client.private.env.json = docs\http-client.private.env.json
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3EC303ED-F0F0-4AFF-8A3E-48B22F831CB3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3EC303ED-F0F0-4AFF-8A3E-48B22F831CB3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3EC303ED-F0F0-4AFF-8A3E-48B22F831CB3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3EC303ED-F0F0-4AFF-8A3E-48B22F831CB3}.Release|Any CPU.Build.0 = Release|Any CPU
		{760B0CA2-7F44-4709-A738-5B89F454DD72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{760B0CA2-7F44-4709-A738-5B89F454DD72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{760B0CA2-7F44-4709-A738-5B89F454DD72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{760B0CA2-7F44-4709-A738-5B89F454DD72}.Release|Any CPU.Build.0 = Release|Any CPU
		{99A3D63F-2CDA-49FA-A1C5-027DA8090C00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{99A3D63F-2CDA-49FA-A1C5-027DA8090C00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{99A3D63F-2CDA-49FA-A1C5-027DA8090C00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{99A3D63F-2CDA-49FA-A1C5-027DA8090C00}.Release|Any CPU.Build.0 = Release|Any CPU
		{6E628078-8FFB-4AF7-955C-64F829F860D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6E628078-8FFB-4AF7-955C-64F829F860D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6E628078-8FFB-4AF7-955C-64F829F860D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6E628078-8FFB-4AF7-955C-64F829F860D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{0C79BA1D-D547-4A7B-8B8F-557FBF068E9F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0C79BA1D-D547-4A7B-8B8F-557FBF068E9F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0C79BA1D-D547-4A7B-8B8F-557FBF068E9F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0C79BA1D-D547-4A7B-8B8F-557FBF068E9F}.Release|Any CPU.Build.0 = Release|Any CPU
		{505B6DB0-7D95-4927-8ED8-87160701B1E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{505B6DB0-7D95-4927-8ED8-87160701B1E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{505B6DB0-7D95-4927-8ED8-87160701B1E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{505B6DB0-7D95-4927-8ED8-87160701B1E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{4FB842DF-6A36-4A49-B06D-A051C0FA0AEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4FB842DF-6A36-4A49-B06D-A051C0FA0AEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4FB842DF-6A36-4A49-B06D-A051C0FA0AEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4FB842DF-6A36-4A49-B06D-A051C0FA0AEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{791A8AA0-1457-482B-BB2B-9B12245BD4C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{791A8AA0-1457-482B-BB2B-9B12245BD4C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{791A8AA0-1457-482B-BB2B-9B12245BD4C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{791A8AA0-1457-482B-BB2B-9B12245BD4C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{72901E8E-6B98-4B0B-8C04-9FAD4788C4AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72901E8E-6B98-4B0B-8C04-9FAD4788C4AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72901E8E-6B98-4B0B-8C04-9FAD4788C4AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72901E8E-6B98-4B0B-8C04-9FAD4788C4AB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{99A3D63F-2CDA-49FA-A1C5-027DA8090C00} = {9759C78D-2BAB-4EA7-ACC4-FA6C3A82BB46}
		{760B0CA2-7F44-4709-A738-5B89F454DD72} = {9759C78D-2BAB-4EA7-ACC4-FA6C3A82BB46}
		{3EC303ED-F0F0-4AFF-8A3E-48B22F831CB3} = {9759C78D-2BAB-4EA7-ACC4-FA6C3A82BB46}
		{6E628078-8FFB-4AF7-955C-64F829F860D1} = {9759C78D-2BAB-4EA7-ACC4-FA6C3A82BB46}
		{0C79BA1D-D547-4A7B-8B8F-557FBF068E9F} = {9759C78D-2BAB-4EA7-ACC4-FA6C3A82BB46}
		{505B6DB0-7D95-4927-8ED8-87160701B1E3} = {5CA3D57C-C4FC-425A-BFE8-E6E74504FEC9}
		{4FB842DF-6A36-4A49-B06D-A051C0FA0AEA} = {5CA3D57C-C4FC-425A-BFE8-E6E74504FEC9}
		{791A8AA0-1457-482B-BB2B-9B12245BD4C5} = {5CA3D57C-C4FC-425A-BFE8-E6E74504FEC9}
		{72901E8E-6B98-4B0B-8C04-9FAD4788C4AB} = {5CA3D57C-C4FC-425A-BFE8-E6E74504FEC9}
	EndGlobalSection
EndGlobal
