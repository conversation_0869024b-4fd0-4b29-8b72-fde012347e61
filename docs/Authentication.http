### Login
POST {{BaseUrl}}/api/v3/authentication/login
Content-Type: application/json

{
  "authIdentities": [
    {
      "authMethod": "Guest",
      "authToken": "guest-token"
    }
  ]
}

> {%
    client.global.set("AccessToken", response.body.authTokens.accessToken);
    client.global.set("RefreshToken", response.body.authTokens.refreshToken);
%}

### Refresh
POST {{BaseUrl}}/api/v3/authentication/refresh
Content-Type: application/json

{
  "refreshToken": "{{RefreshToken}}"
}

> {%
    client.global.set("AccessToken", response.body.authTokens.accessToken);
    client.global.set("RefreshToken", response.body.authTokens.refreshToken);
%}
