# Build results
bin/
obj/
*.exe
*.dll
*.app
*.log
*.user
*.suo
*.cache
*.pdb
*.mdb

# Rider
.idea/
.idea*/**
.idea_modules/
*.sln.iml

# VS Code
.vscode/

# VS / MSBuild
*.suo
*.user
*.userosscache
*.sln.docstates
*.VC.db
.vs/

# NuGet
*.nupkg
*.snupkg
*.nuspec
.nuget/
packages/
project.lock.json
project.fragment.lock.json
artifacts/

# Resharper
_ReSharper*/
*.DotSettings.user
*.DotCover
*.ReSharper

# Test results
TestResults/
*.trx
*.coverage
*.coveragexml
*.testresults

# ASP.NET / EF / Identity
appsettings.Development.json
appsettings.Local.json
appsettings.*.local.json
secrets.json

# Logs and dumps
logs/
*.log
*.dmp

# Tye
.tye/

# EF Migrations snapshots
**/Migrations/*Snapshot.cs

# Local tools
tools/


# Docker artifacts
docker-compose.override.yml
**/docker-compose*.yml
**/*.db

# Ignore generated swagger docs if generated locally
swagger/
*.swagger.json

# Others
*.tmp
*.bak
*.old
*.orig
*.swp
*.swo

# OS junk
.DS_Store
Thumbs.db
ehthumbs.db
desktop.ini

# Environment and secrets
.env
.env.*
*.env
*.env.*