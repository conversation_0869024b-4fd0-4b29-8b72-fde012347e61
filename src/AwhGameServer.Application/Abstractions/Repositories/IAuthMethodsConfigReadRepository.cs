using AwhGameServer.Domain.Aggregates.Game;

namespace AwhGameServer.Application.Abstractions.Repositories;

/// <summary>
/// Репозиторий для чтения конфигурации методов аутентификации.
/// </summary>
public interface IAuthMethodsConfigReadRepository : IRepository<AuthMethodsConfig>
{
    /// <summary>
    /// Получить текущую конфигурацию методов аутентификации.
    /// </summary>
    /// <param name="cancellationToken">Токен отмены.</param>
    /// <returns>Всегда возвращает текущую конфигурацию (<see cref="AuthMethodsConfig"/>).</returns>
    Task<AuthMethodsConfig> GetConfigAsync(CancellationToken cancellationToken = default);
}
