using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.ValueObjects.Users;

namespace AwhGameServer.Application.Abstractions.Repositories;

/// <summary>
/// Репозиторий для работы с точками входа в пользовательский аккаунт (<see cref="AuthIdentity"/>).
/// </summary>
public interface IAuthIdentityRepository : IRepository<AuthIdentity>
{
    /// <summary>
    /// Получить точку входа по идентификатору.
    /// </summary>
    /// <param name="sessionAuthIdentityId">Идентификатор точки входа.</param>
    /// <param name="ct">Токен отмены.</param>
    Task<AuthIdentity?> GetByIdAsync(AuthIdentityId sessionAuthIdentityId, CancellationToken ct);
    
    /// <summary>
    /// Получить точку входа по методу аутентификации и токену аутентификации.
    /// </summary>
    /// <param name="authMethod">Ключ метода аутентификации.</param>
    /// <param name="authToken">Токен аутентификации.</param>
    /// <param name="cancellationToken">Токен отмены.</param>
    /// <returns>Возвращает найденную точку входа (<see cref="AuthIdentity"/>) или <see langword="null"/>, если не найдена.</returns>
    Task<AuthIdentity?> GetByAuthMethodAndTokenAsync(string authMethod, string authToken, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Получить все точки входа для указанного пользователя.
    /// </summary>
    /// <param name="userId">Идентификатор пользователя.</param>
    /// <param name="cancellationToken">Токен отмены.</param>
    /// <returns>Возвращает коллекцию точек входа (<see cref="AuthIdentity"/>) или пустую коллекцию, если не найдено.</returns>
    Task<IReadOnlyCollection<AuthIdentity>> GetByUserIdAsync(UserId userId, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Сохранить точку входа в пользовательский аккаунт.
    /// </summary>
    /// <param name="authIdentity">Точка входа (<see cref="AuthIdentity"/>).</param>
    /// <param name="cancellationToken">Токен отмены.</param>
    Task AddAsync(AuthIdentity authIdentity, CancellationToken cancellationToken = default);
}
