using AwhGameServer.Application.Abstractions.Models;

namespace AwhGameServer.Application.Abstractions.Services;

/// <summary>
/// Сервис для хеширования токенов.
/// Хеширование используется для хранения токенов в БД, чтобы не хранить их в открытом виде.
/// </summary>
public interface ITokenHasher
{
    public Task<TokenHash> HashToken(string token, CancellationToken cancellationToken = default);
}
