namespace AwhGameServer.Application.Abstractions.Messaging;

/// <summary>
/// Контракт обработчика запроса в паттерне CQRS.
/// </summary>
/// <typeparam name="TQ<PERSON><PERSON>">Тип запроса, который обрабатывает данный обработчик.</typeparam>
/// <typeparam name="TResponse">Тип результата, возвращаемого после выполнения запроса.</typeparam>
/// <remarks>
/// Обработчик инкапсулирует логику получения данных из домена или внешних источников,
/// не изменяя состояние системы.
/// </remarks>
public interface IQueryHandler<in TQuery, TResponse>
    where TQuery : IQuery<TResponse>
{
    /// <summary>
    /// Выполняет указанный запрос.
    /// </summary>
    /// <param name="query">Запрос, который необходимо выполнить.</param>
    /// <param name="ct">Токен отмены.</param>
    /// <returns>Результат выполнения запроса.</returns>
    Task<TResponse> Handle(TQuery query, CancellationToken ct);
}
