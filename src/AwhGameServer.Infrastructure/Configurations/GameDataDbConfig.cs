using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Конфигурация базы игровых данных.
/// </summary>
public class GameDataDbConfig
{
    /// <summary>
    /// Имя базы данных MongoDB для хранения конфигурационных данных игры.
    /// </summary>
    [Required]
    public string DatabaseName { get; set; } = null!;

    /// <summary>
    /// Путь к JSON-файлу с начальными данными для заполнения БД.
    /// Может быть абсолютным или относительным к директории приложения.
    /// Используется при первом создании БД для загрузки базовых данных.
    /// </summary>
    public string GameDataDbSeedJsonPath { get; set; } = null!;
}
