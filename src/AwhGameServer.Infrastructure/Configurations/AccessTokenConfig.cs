using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Конфигурация сессий аутентификации.
/// Содержит параметры для генерации access и refresh токенов.
/// </summary>
public class AuthSessionConfig
{
    [Required]
    public string AccessTokenIssuer { get; set; } = string.Empty;
    
    [Required]
    public string AccessTokenAudience { get; set; } = string.Empty;
    
    [Required]
    public int AccessTokenLifetimeMinutes { get; set; } = 15;
    
    [Required]
    [MinLength(32)]
    public string AccessTokenSecretBase64 { get; set; } = string.Empty;
    
    [Required]
    public int RefreshTokenLifetimeDays { get; set; } = 30;
}
