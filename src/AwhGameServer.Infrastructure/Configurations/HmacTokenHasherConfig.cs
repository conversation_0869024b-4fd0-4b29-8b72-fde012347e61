using System.ComponentModel.DataAnnotations;

namespace AwhGameServer.Infrastructure.Configurations;

/// <summary>
/// Конфигурация для HMAC-хешера токенов.
/// Содержит секретный ключ (pepper), используемый для хеширования токенов.
/// </summary>
public class HmacTokenHasherConfig
{
    /// <summary>
    /// Секретный ключ (pepper) в формате Base64 для HMAC-хеширования токенов.
    /// Pepper - это общий секрет, известный только серверу, который добавляется
    /// к токену перед хешированием для защиты от атак при компрометации БД.
    /// Минимальная длина - 32 символа для обеспечения криптографической стойкости.
    /// </summary>
    [Required]
    [MinLength(32)]
    public string PepperBase64 { get; set; } = string.Empty;
}
