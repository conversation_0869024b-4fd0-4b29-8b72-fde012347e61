using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Application.Abstractions.Services;
using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Application.Abstractions.Stores;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Infrastructure.Configurations;
using AwhGameServer.Infrastructure.Persistence.Ef;
using AwhGameServer.Infrastructure.Persistence.Ef.Hosting;
using AwhGameServer.Infrastructure.Services;
using AwhGameServer.Infrastructure.Repositories;
using AwhGameServer.Infrastructure.Stores;
using AwhGameServer.Infrastructure.UnitOfWork;

namespace AwhGameServer.Infrastructure.Extensions;

/// <summary>
/// Методы расширения для регистрации сервисов слоя инфраструктуры в контейнере зависимостей.
/// Предоставляет централизованную точку для настройки всех компонентов инфраструктуры.
/// </summary>
public static class InfrastructureServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует все сервисы слоя инфраструктуры в контейнере зависимостей.
    /// </summary>
    ///
    /// <remarks>
    /// Включает регистрацию:
    /// <list type="bullet">
    /// <item><description>Сессии аутентификации <see cref="IAuthTokensGenerator"/>, <see cref="IAuthSessionStore"/></description></item>
    /// <item><description>Хеширование токенов <see cref="ITokenHasher"/></description></item>
    /// <item><description>Генераторы идентификаторов <see cref="ITypedIdGenerator{TId}"/></description></item>
    /// <item><description>Клиент MongoDB <see cref="IMongoClient"/></description></item>
    /// <item><description>Базу игровых данных <see cref="GameDataDbContext"/>
    /// и сервис её инициализации <see cref="GameDataDbInitializationHostedService"/></description></item>
    /// <item><description>Базу пользовательских данных <see cref="UsersDataDbContext"/>
    /// и сервис её инициализации <see cref="UsersDataDbInitializationHostedService"/></description></item>
    /// </list>
    /// </remarks>
    /// 
    /// <param name="services">Коллекция сервисов для регистрации.</param>
    /// <param name="configuration">Конфигурация приложения.</param>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddAuthSession();
        
        services.AddTokenHasher();
        
        services.AddIdGenerators();
        
        services.AddStores();

        services.AddMongoClient();
        services.AddGameDataDb();
        services.AddUsersDataDb();
        services.AddRepositories();
        services.AddUnitOfWork();
        
        return services;
    }
    
    /// <summary>
    /// Регистрирует сервисы аутентификации.
    /// </summary>
    private static IServiceCollection AddAuthSession(this IServiceCollection services)
    {
        services
            .AddOptions<AuthSessionConfig>()
            .BindConfiguration(nameof(AuthSessionConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();
        
        services.AddSingleton<IAuthTokensGenerator, AuthTokensGenerator>();
        
        return services;
    }

    /// <summary>
    /// Регистрирует сервис хеширования токенов и его конфигурацию.
    /// </summary>
    private static IServiceCollection AddTokenHasher(this IServiceCollection services)
    {
        services
            .AddOptions<HmacTokenHasherConfig>()
            .BindConfiguration(nameof(HmacTokenHasherConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddSingleton<ITokenHasher, HmacTokenHasher>();

        return services;
    }
    
    /// <summary>
    /// Регистрирует генераторы идентификаторов в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddIdGenerators(this IServiceCollection services)
    {
        services
            .AddOptions<UserIdGeneratorConfig>()
            .BindConfiguration(nameof(UserIdGeneratorConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();
        
        services.AddScoped<ITypedIdGenerator<UserId>, UserIdGenerator<UserId>>();

        services.AddSingleton<ITypedIdGenerator<AuthIdentityId>, MongoObjectIdGenerator<AuthIdentityId>>();
        
        return services;
    }
    
    /// <summary>
    /// Регистрирует хранилища в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddStores(this IServiceCollection services)
    {
        services.AddSingleton<IAuthSessionStore, InMemoryAuthSessionStore>();
        
        return services;
    }

    /// <summary>
    /// Регистрирует клиент MongoDB и конфигурацию персистентности.
    /// </summary>
    private static IServiceCollection AddMongoClient(this IServiceCollection services)
    {
        services
            .AddOptions<PersistenceConfig>()
            .BindConfiguration(nameof(PersistenceConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddSingleton<IMongoClient>(serviceProvider =>
        {
            var persistenceConfig = serviceProvider.GetRequiredService<IOptions<PersistenceConfig>>().Value;

            return new MongoClient(persistenceConfig.MongoDbConnectionString);
        });

        return services;
    }

    /// <summary>
    /// Регистрирует контекст базы игровых данных и сервис её инициализации.
    /// </summary>
    private static IServiceCollection AddGameDataDb(this IServiceCollection services)
    {
        services.AddOptions<GameDataDbConfig>()
            .BindConfiguration(nameof(PersistenceConfig) + ":" + nameof(GameDataDbConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddDbContext<GameDataDbContext>((serviceProvider, options) =>
        {
            var client = serviceProvider.GetRequiredService<IMongoClient>();
            var gameDataDbConfig = serviceProvider.GetRequiredService<IOptions<GameDataDbConfig>>().Value;

            options.UseMongoDB(client, gameDataDbConfig.DatabaseName);
        });

        services.AddHostedService<GameDataDbInitializationHostedService>();

        return services;
    }

    /// <summary>
    /// Регистрирует контекст базы пользовательских данных и сервис её инициализации.
    /// </summary>
    private static IServiceCollection AddUsersDataDb(this IServiceCollection services)
    {
        services.AddOptions<UsersDataDbConfig>()
            .BindConfiguration(nameof(PersistenceConfig) + ":" + nameof(UsersDataDbConfig))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddDbContext<UsersDataDbContext>((serviceProvider, options) =>
        {
            var client = serviceProvider.GetRequiredService<IMongoClient>();
            var usersDataDbConfig = serviceProvider.GetRequiredService<IOptions<UsersDataDbConfig>>().Value;

            options.UseMongoDB(client, usersDataDbConfig.DatabaseName);
        });

        services.AddHostedService<UsersDataDbInitializationHostedService>();

        return services;
    }

    /// <summary>
    /// Регистрирует репозитории в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        services.AddScoped<IAuthIdentityRepository, AuthIdentityRepository>();
        services.AddScoped<IAuthMethodsConfigReadRepository, AuthMethodsConfigReadRepository>();

        return services;
    }

    /// <summary>
    /// Регистрирует единицы работы в контейнере зависимостей.
    /// </summary>
    private static IServiceCollection AddUnitOfWork(this IServiceCollection services)
    {
        services.AddScoped<IAuthenticationUow, AuthenticationUow>();

        return services;
    }
}
