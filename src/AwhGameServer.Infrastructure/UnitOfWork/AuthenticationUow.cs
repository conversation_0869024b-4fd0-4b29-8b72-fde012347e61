using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Application.Abstractions.UnitOfWork;
using AwhGameServer.Infrastructure.Persistence.Ef;

namespace AwhGameServer.Infrastructure.UnitOfWork;

/// <summary>
/// Реализация единицы работы для операций аутентификации.
/// Объединяет репозитории для работы с данными аутентификации и обеспечивает транзакционность операций.
/// </summary>
/// <param name="usersDataDbContext">Контекст базы пользовательских данных.</param>
/// <param name="authIdentityRepository">Репозиторий для работы с точками входа.</param>
/// <param name="authMethodsConfigRepository">Репозиторий для чтения конфигурации методов аутентификации.</param>
public class AuthenticationUow(
    UsersDataDbContext usersDataDbContext,
    IAuthIdentityRepository authIdentityRepository,
    IAuthMethodsConfigReadRepository authMethodsConfigRepository)
    : IAuthenticationUow
{
    /// <inheritdoc />
    public IAuthIdentityRepository AuthIdentityRepository { get; } = authIdentityRepository;

    /// <inheritdoc />
    public IAuthMethodsConfigReadRepository AuthMethodsConfigRepository { get; } = authMethodsConfigRepository;

    /// <inheritdoc />
    public async Task SaveChangesAsync(CancellationToken ct = default)
    {
        await usersDataDbContext.SaveChangesAsync(ct);
    }
}
