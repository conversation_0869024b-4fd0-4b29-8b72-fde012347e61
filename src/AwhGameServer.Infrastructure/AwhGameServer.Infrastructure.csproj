<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\AwhGameServer.Application\AwhGameServer.Application.csproj" />
      <ProjectReference Include="..\AwhGameServer.Domain\AwhGameServer.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.Options.DataAnnotations" Version="9.0.7" />
        <PackageReference Include="MongoDB.EntityFrameworkCore" Version="9.0.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    </ItemGroup>

</Project>
