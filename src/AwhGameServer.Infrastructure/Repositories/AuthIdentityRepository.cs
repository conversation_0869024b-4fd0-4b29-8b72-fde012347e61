using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Application.Abstractions.Repositories;
using AwhGameServer.Infrastructure.Persistence.Ef;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.UsersData;

namespace AwhGameServer.Infrastructure.Repositories;

/// <summary>
/// Реализация репозитория для работы с точками входа в пользовательский аккаунт.
/// Обеспечивает доступ к данным <see cref="AuthIdentity"/> через Entity Framework и MongoDB.
/// </summary>
/// <param name="context">Контекст базы пользовательских данных.</param>
public class AuthIdentityRepository(UsersDataDbContext context) : IAuthIdentityRepository
{
    /// <inheritdoc />
    public async Task<AuthIdentity?> GetByIdAsync(AuthIdentityId authIdentityId, CancellationToken ct)
    {
        if (!ObjectId.TryParse(authIdentityId.Value, out var objectId))
            return null;

        var document = await context.AuthIdentities
            .FirstOrDefaultAsync(x => x.Id == objectId, ct);

        return document == null ? null : MapToDomain(document);
    }

    /// <inheritdoc />
    public async Task<AuthIdentity?> GetByAuthMethodAndTokenAsync(string authMethod, string authToken, CancellationToken cancellationToken = default)
    {
        var document = await context.AuthIdentities
            .FirstOrDefaultAsync(x => x.AuthMethod == authMethod && x.AuthToken == authToken, cancellationToken);

        return document == null ? null : MapToDomain(document);
    }

    /// <inheritdoc />
    public async Task<IReadOnlyCollection<AuthIdentity>> GetByUserIdAsync(UserId userId, CancellationToken cancellationToken = default)
    {
        var documents = await context.AuthIdentities
            .Where(x => x.UserId == userId.Value)
            .ToListAsync(cancellationToken);

        return documents.Select(MapToDomain).ToList();
    }

    /// <inheritdoc />
    public async Task AddAsync(AuthIdentity authIdentity, CancellationToken cancellationToken = default)
    {
        var document = MapToDocument(authIdentity);
        await context.AuthIdentities.AddAsync(document, cancellationToken);
    }

    /// <summary>
    /// Преобразует документ MongoDB в доменную модель.
    /// </summary>
    /// <param name="document">Документ из базы данных.</param>
    /// <returns>Доменная модель точки входа.</returns>
    private static AuthIdentity MapToDomain(AuthIdentityDocument document)
    {
        return new AuthIdentity(
            new AuthIdentityId(document.Id.ToString()),
            new UserId(document.UserId),
            document.AuthToken,
            document.AuthMethod
        );
    }

    /// <summary>
    /// Преобразует доменную модель в документ MongoDB.
    /// </summary>
    /// <param name="authIdentity">Доменная модель точки входа.</param>
    /// <returns>Документ для сохранения в базе данных.</returns>
    private static AuthIdentityDocument MapToDocument(AuthIdentity authIdentity)
    {
        var document = new AuthIdentityDocument
        {
            UserId = authIdentity.UserId.Value,
            AuthMethod = authIdentity.AuthMethod,
            AuthToken = authIdentity.AuthToken
        };

        if (ObjectId.TryParse(authIdentity.Id.Value, out var objectId))
        {
            document.Id = objectId;
        }

        return document;
    }
}
