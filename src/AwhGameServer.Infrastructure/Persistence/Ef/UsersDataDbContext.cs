using Microsoft.EntityFrameworkCore;
using AwhGameServer.Domain.Aggregates.Users;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.UsersData;
using AwhGameServer.Infrastructure.Persistence.Ef.ModelsConfigurations.UsersData;

namespace AwhGameServer.Infrastructure.Persistence.Ef;

/// <summary>
/// Контекст Entity Framework для базы пользовательских данных.
/// Предоставляет доступ к данным пользователей, включая идентичности аутентификации.
/// Использует MongoDB в качестве провайдера базы данных.
/// </summary>
public sealed class UsersDataDbContext : DbContext
{
    /// <summary>
    /// Набор идентичностей аутентификации пользователей.
    /// Каждая запись связывает пользователя с конкретным методом аутентификации.
    /// Представляет собой модель хранения <see cref="AuthIdentity"/> из домена.
    /// </summary>
    public DbSet<AuthIdentityDocument> AuthIdentities { get; set; } = null!;

    /// <summary>
    /// Счётчики для хранения последовательных номеров.
    /// </summary>
    public DbSet<CounterDocument> Counters { get; set; } = null!;

    /// <summary>
    /// Инициализирует новый экземпляр контекста базы данных пользовательских данных.
    /// Он пустой, так как Entity Framework требует конструктор с параметром options.
    /// </summary>
    public UsersDataDbContext(DbContextOptions<UsersDataDbContext> options) : base(options)
    {

    }

    /// <summary>
    /// Настраивает модель данных при создании контекста.
    /// Применяет конфигурации Entity Framework для всех сущностей.
    /// </summary>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new AuthIdentityDocumentConfiguration());
        modelBuilder.ApplyConfiguration(new CounterDocumentConfiguration());

        base.OnModelCreating(modelBuilder);
    }
}
