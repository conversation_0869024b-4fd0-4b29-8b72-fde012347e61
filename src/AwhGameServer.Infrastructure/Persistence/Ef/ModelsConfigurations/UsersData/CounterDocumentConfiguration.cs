using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MongoDB.EntityFrameworkCore.Extensions;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.UsersData;

namespace AwhGameServer.Infrastructure.Persistence.Ef.ModelsConfigurations.UsersData;

/// <summary>
/// Конфигурация Entity Framework для сущности CounterDocument.
/// Определяет маппинг между C# классом и коллекцией MongoDB,
/// включая имена полей, индексы и ограничения для счетчиков.
/// </summary>
public class CounterDocumentConfiguration : IEntityTypeConfiguration<CounterDocument>
{
    /// <summary>
    /// Настраивает маппинг сущности CounterDocument для MongoDB.
    /// Создает уникальный индекс по полю CounterKey для обеспечения
    /// уникальности ключей счетчиков в коллекции.
    /// </summary>
    public void Configure(EntityTypeBuilder<CounterDocument> builder)
    {
        builder.ToCollection("counters");

        builder
            .HasKey(x => x.Id);

        builder
            .HasIndex(x => x.CounterKey)
            .IsUnique();
        builder
            .Property(x => x.CounterKey)
            .HasElementName("counter_key")
            .IsRequired();

        builder
            .Property(x => x.Value)
            .HasElementName("value")
            .IsRequired();
    }
}
