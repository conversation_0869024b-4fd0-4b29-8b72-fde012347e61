using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MongoDB.EntityFrameworkCore.Extensions;
using AwhGameServer.Infrastructure.Persistence.Ef.Models.GameData;

namespace AwhGameServer.Infrastructure.Persistence.Ef.ModelsConfigurations.GameData;

/// <summary>
/// Конфигурация Entity Framework для сущности AuthMethodDocument.
/// Определяет маппинг между C# классом и коллекцией MongoDB,
/// включая имена полей, индексы и ограничения.
/// </summary>
public class AuthMethodDocumentConfiguration
    : IEntityTypeConfiguration<AuthMethodDocument>
{
    public void Configure(EntityTypeBuilder<AuthMethodDocument> builder)
    {
        builder.ToCollection("auth_methods");

        builder
            .HasKey(x => x.Id);

        builder
            .HasIndex(x => x.MethodKey)
            .IsUnique();
        builder
            .Property(x => x.MethodKey)
            .HasElementName("method_key")
            .IsRequired();

        builder
            .Property(x => x.IsRegistrationAllowed)
            .HasElementName("is_registration_allowed")
            .IsRequired();

        builder
            .Property(x => x.IsLoginAllowed)
            .HasElementName("is_login_allowed")
            .IsRequired();
    }
}
