using Newtonsoft.Json;
using AwhGameServer.Infrastructure.Configurations;

namespace AwhGameServer.Infrastructure.Persistence.Ef.Seed;

/// <summary>
/// Статический класс для заполнения базы данных игровых данных начальными значениями.
/// Загружает данные из JSON-файла и добавляет их в базу данных.
/// </summary>
public static class GameDataDbSeeder
{
    /// <summary>
    /// Заполняет базу данных игровых данных начальными значениями из JSON-файла.
    /// Если файл не существует, операция пропускается без ошибок.
    /// </summary>
    /// <param name="db">Контекст базы данных игровых данных.</param>
    /// <param name="config">Конфигурация с путем к файлу начальных данных.</param>
    /// <param name="ct">Токен отмены операции.</param>
    public static async Task Seed(GameDataDbContext db, GameDataDbConfig config, CancellationToken ct = default)
    {
        var path = Path.IsPathRooted(config.GameDataDbSeedJsonPath)
            ? config.GameDataDbSeedJsonPath
            : Path.Combine(AppContext.BaseDirectory, config.GameDataDbSeedJsonPath);

        if (!File.Exists(path))
            return;

        var json = await File.ReadAllTextAsync(path, ct);
        var seed = JsonConvert.DeserializeObject<GameDataDbSeed>(json);

        if (seed?.AuthMethods is not null)
            db.AuthMethods.AddRange(seed.AuthMethods);

        await db.SaveChangesAsync(ct);
    }
}
