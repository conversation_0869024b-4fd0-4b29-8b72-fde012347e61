using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace AwhGameServer.Infrastructure.Persistence.Ef.Hosting;

/// <summary>
/// Фоновый сервис для инициализации базы данных пользовательских данных при запуске приложения.
/// Создает БД если она не существует.
/// </summary>
/// <param name="serviceProvider">Провайдер сервисов для создания области видимости.</param>
public class UsersDataDbInitializationHostedService(IServiceProvider serviceProvider) : IHostedService
{
    /// <summary>
    /// Выполняется при запуске приложения.
    /// Создает базу данных пользовательских данных если она не существует.
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        using var scope = serviceProvider.CreateScope();

        var db = scope.ServiceProvider.GetRequiredService<UsersDataDbContext>();

        var justCreated = await db.Database.EnsureCreatedAsync(cancellationToken);
    }

    /// <summary>
    /// Выполняется при остановке приложения. Не требует дополнительных действий.
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}
