using AwhGameServer.Domain.ValueObjects.Users;
using AwhGameServer.Application.Abstractions.Models;
using AwhGameServer.Application.Abstractions.Stores;

namespace AwhGameServer.Infrastructure.Stores;

/// <summary>
/// Реализация хранилища сессий аутентификации в памяти.
/// Предназначена для разработки, тестирования и демонстрационных целей.
/// </summary>
/// <remarks>
/// Данная реализация хранит все сессии в памяти приложения и не обеспечивает персистентность данных.
/// При перезапуске приложения все сессии будут потеряны.
/// Автоматически удаляет истекшие сессии при обращении к ним.
/// Не рекомендуется для использования в продакшене из-за отсутствия персистентности и ограничений масштабируемости.
///
/// ВАЖНО: Данная реализация является thread-safe и может безопасно использоваться как Singleton
/// в многопоточной среде благодаря использованию ReaderWriterLockSlim для синхронизации доступа.
/// </remarks>
public class InMemoryAuthSessionStore : IAuthSessionStore, IDisposable
{
    /// <summary>
    /// Коллекция активных сессий аутентификации в памяти.
    /// </summary>
    private readonly List<AuthSession> _sessions = [];

    /// <summary>
    /// Блокировка для обеспечения thread-safety при работе с коллекцией сессий.
    /// Использует ReaderWriterLockSlim для оптимизации производительности чтения.
    /// </summary>
    private readonly ReaderWriterLockSlim _lock = new();

    /// <summary>
    /// Получает сессию аутентификации по идентификатору сессии.
    /// </summary>
    /// <param name="sessionId">Уникальный идентификатор сессии.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>
    /// Сессию аутентификации, если найдена и не истекла, иначе <see langword="null"/>.
    /// </returns>
    /// <remarks>
    /// Автоматически удаляет сессию из хранилища, если она истекла.
    /// </remarks>
    public Task<AuthSession?> GetSessionAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        _lock.EnterUpgradeableReadLock();
        try
        {
            var session = _sessions.FirstOrDefault(x => x.SessionId == sessionId);

            if (session is not null && HandleSessionExpirationUnsafe(session))
                session = null;

            return Task.FromResult(session);
        }
        finally
        {
            _lock.ExitUpgradeableReadLock();
        }
    }

    /// <summary>
    /// Получает сессию аутентификации по хешу refresh токена.
    /// </summary>
    /// <param name="refreshTokenHash">Хеш refresh токена для поиска сессии.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>
    /// Сессию аутентификации, если найдена и не истекла, иначе <see langword="null"/>.
    /// </returns>
    /// <remarks>
    /// Автоматически удаляет сессию из хранилища, если она истекла.
    /// </remarks>
    public Task<AuthSession?> GetSessionByRefreshHashAsync(TokenHash refreshTokenHash, CancellationToken cancellationToken = default)
    {
        _lock.EnterUpgradeableReadLock();
        try
        {
            var session = _sessions.FirstOrDefault(x => x.RefreshHash == refreshTokenHash.HashBase64Url);

            if (session is not null && HandleSessionExpirationUnsafe(session))
                session = null;

            return Task.FromResult(session);
        }
        finally
        {
            _lock.ExitUpgradeableReadLock();
        }
    }

    /// <summary>
    /// Атомарно отзывает все существующие сессии пользователя и создает новую сессию.
    /// </summary>
    /// <param name="sessionId">Уникальный идентификатор новой сессии.</param>
    /// <param name="userId">Идентификатор пользователя.</param>
    /// <param name="refreshTokenHash">Хеш refresh токена для новой сессии.</param>
    /// <param name="authIdentityId">Идентификатор метода аутентификации.</param>
    /// <param name="expiresAtUtc">Время истечения новой сессии в UTC.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>Задача, представляющая асинхронную операцию.</returns>
    /// <exception cref="InvalidOperationException">
    /// Выбрасывается, если сессия с указанным sessionId уже существует и принадлежит другому пользователю.
    /// </exception>
    /// <remarks>
    /// Операция выполняется атомарно: сначала удаляются все существующие сессии пользователя,
    /// затем создается новая сессия. SessionId должен быть уникальным в рамках всего хранилища.
    /// </remarks>
    public Task RevokeAllUserSessionsThenCreateAsync(string sessionId, UserId userId, TokenHash refreshTokenHash,
        AuthIdentityId authIdentityId, DateTime expiresAtUtc, CancellationToken cancellationToken = default)
    {
        _lock.EnterWriteLock();
        try
        {
            // Проверяем на коллизию sessionId перед удалением сессий пользователя
            var collidingSession = _sessions.FirstOrDefault(x => x.SessionId == sessionId);
            if (collidingSession is not null)
            {
                // Если коллизия с сессией другого пользователя - это ошибка
                if (collidingSession.UserId != userId)
                {
                    throw new InvalidOperationException(
                        $"Session with ID '{sessionId}' already exists for a different user. " +
                        $"SessionId must be unique across all users.");
                }
                // Если это сессия того же пользователя, она будет удалена ниже при RemoveAll
            }

            _sessions.RemoveAll(x => x.UserId == userId);

            var session = new AuthSession(
                sessionId,
                userId,
                refreshTokenHash.HashBase64Url,
                authIdentityId,
                false,
                null,
                DateTime.UtcNow,
                expiresAtUtc);

            _sessions.Add(session);

            return Task.CompletedTask;
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Пытается обновить refresh токен в существующей сессии.
    /// </summary>
    /// <param name="expectedOldRefreshTokenHash">Ожидаемый хеш текущего refresh токена.</param>
    /// <param name="newRefreshTokenHash">Новый хеш refresh токена.</param>
    /// <param name="newExpiresAtUtc">Новое время истечения сессии.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns>
    /// <see langword="true"/>, если сессия найдена и успешно обновлена;
    /// <see langword="false"/>, если сессия не найдена или истекла.
    /// </returns>
    /// <remarks>
    /// Операция выполняется атомарно: проверяется существование сессии с указанным хешем,
    /// затем обновляется хеш refresh токена и время истечения.
    /// Автоматически удаляет сессию, если она истекла.
    /// </remarks>
    public Task<bool> TryRotateRefreshAsync(TokenHash expectedOldRefreshTokenHash, TokenHash newRefreshTokenHash,
        DateTimeOffset newExpiresAtUtc, CancellationToken cancellationToken = default)
    {
        _lock.EnterUpgradeableReadLock();
        try
        {
            var session = _sessions.FirstOrDefault(x => x.RefreshHash == expectedOldRefreshTokenHash.HashBase64Url);

            if (session is null || HandleSessionExpirationUnsafe(session))
                return Task.FromResult(false);

            // Переходим к write lock для обновления
            _lock.EnterWriteLock();
            try
            {
                // Повторно ищем сессию, так как она могла измениться
                var currentIndex = _sessions.FindIndex(x => x.RefreshHash == expectedOldRefreshTokenHash.HashBase64Url);
                if (currentIndex == -1)
                    return Task.FromResult(false);

                var updatedSession = _sessions[currentIndex] with
                {
                    RefreshHash = newRefreshTokenHash.HashBase64Url,
                    ExpiresAtUtc = newExpiresAtUtc.UtcDateTime
                };

                _sessions[currentIndex] = updatedSession;

                return Task.FromResult(true);
            }
            finally
            {
                _lock.ExitWriteLock();
            }
        }
        finally
        {
            _lock.ExitUpgradeableReadLock();
        }
    }

    /// <summary>
    /// Обрабатывает истечение срока действия сессии (небезопасная версия для использования внутри блокировок).
    /// </summary>
    /// <param name="session">Сессия для проверки на истечение.</param>
    /// <returns>
    /// <see langword="true"/>, если сессия истекла и была удалена из хранилища;
    /// <see langword="false"/>, если сессия не истекла или равна <see langword="null"/>.
    /// </returns>
    /// <remarks>
    /// Если сессия истекла (время истечения меньше текущего времени UTC),
    /// она автоматически удаляется из коллекции сессий.
    ///
    /// 
    /// Этот метод НЕ является thread-safe и должен вызываться только внутри блокировок!!!!!!
    /// </remarks>
    private bool HandleSessionExpirationUnsafe(AuthSession? session)
    {
        if (session is null) return false;

        if (session.ExpiresAtUtc >= DateTime.UtcNow) return false;

        // Для удаления нужна write блокировка
        _lock.EnterWriteLock();
        try
        {
            _sessions.Remove(session);
            return true;
        }
        finally
        {
            _lock.ExitWriteLock();
        }
    }

    /// <summary>
    /// Освобождает ресурсы, используемые объектом.
    /// </summary>
    public void Dispose()
    {
        _lock?.Dispose();
    }
}
