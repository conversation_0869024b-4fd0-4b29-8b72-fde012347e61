using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Authentication;

/// <summary>
/// Маппер для преобразования результатов обновления токенов из уровня приложения в контрактные модели.
/// </summary>
public static class RefreshResponseMapper
{
    /// <summary>
    /// Преобразует результат команды обновления токенов уровня приложения в контрактный ответ.
    /// </summary>
    /// <param name="refreshCommandResult">Результат команды обновления токенов уровня приложения</param>
    /// <returns>Контрактный ответ на запрос обновления токенов</returns>
    public static RefreshResponse MapToContract(this RefreshCommandResult refreshCommandResult)
    {
        return new RefreshResponse
        {
            AuthTokens = refreshCommandResult.AuthTokens.MapToContract()
        };
    }
}
