using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Authentication;

/// <summary>
/// Маппер для преобразования запросов на вход из контрактной модели в команды уровня приложения.
/// </summary>
public static class LoginRequestMapper
{
    /// <summary>
    /// Преобразует контрактный запрос на вход в команду уровня приложения.
    /// </summary>
    /// <param name="loginRequest">Контрактный запрос на вход</param>
    /// <returns>Команда входа для уровня приложения</returns>
    public static LoginCommand MapToApplicationCommand(this LoginRequest loginRequest)
    {
        return new LoginCommand(loginRequest.AuthIdentities.Select(x => x.MapToApplicationDto()).ToList());
    }
}
