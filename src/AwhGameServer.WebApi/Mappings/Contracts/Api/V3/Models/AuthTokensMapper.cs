using AwhGameServer.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Models;

/// <summary>
/// Маппер для преобразования между контрактными и доменными моделями токенов аутентификации.
/// </summary>
public static class AuthTokensMapper
{
    /// <summary>
    /// Преобразует DTO токенов аутентификации уровня приложения в контрактную модель.
    /// </summary>
    /// <param name="authTokensDto">DTO токенов аутентификации уровня приложения</param>
    /// <returns>Контрактная модель токенов аутентификации</returns>
    public static AuthTokens MapToContract(this Application.Dto.AuthTokensDto authTokensDto)
    {
        return new AuthTokens
        {
            AccessToken = authTokensDto.AccessToken,
            RefreshToken = authTokensDto.RefreshToken
        };
    }
}
