using FluentValidation;
using AwhGameServer.Contracts.Api.V3.Authentication;

namespace AwhGameServer.WebApi.Validation.Contracts.Api.V3.Authentication;

/// <summary>
/// Валидатор для запросов на обновление токенов доступа.
/// Проверяет корректность и полноту refresh token.
/// </summary>
public class RefreshRequestValidator : AbstractValidator<RefreshRequest>
{
    public RefreshRequestValidator()
    {
        RuleFor(x => x.RefreshToken)
            .NotNull()
            .NotEmpty()
            .WithMessage("Refresh token cannot be null or empty");
    }
}
