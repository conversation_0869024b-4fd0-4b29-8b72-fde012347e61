using FluentValidation;
using AwhGameServer.Contracts.Api.V3.Models;

namespace AwhGameServer.WebApi.Validation.Contracts.Api.V3.Models;

/// <summary>
/// Валидатор для идентификационных данных аутентификации.
/// Проверяет корректность метода аутентификации и токена.
/// </summary>
public class AuthIdentityValidator : AbstractValidator<AuthIdentity>
{
    public AuthIdentityValidator()
    {
        RuleFor(x => x.AuthMethod)
            .NotNull()
            .NotEmpty()
            .WithMessage("Auth method cannot be null or empty");
        
        RuleFor(x => x.AuthToken)
            .NotNull()
            .NotEmpty()
            .WithMessage("Auth token cannot be null or empty");
    }
}
