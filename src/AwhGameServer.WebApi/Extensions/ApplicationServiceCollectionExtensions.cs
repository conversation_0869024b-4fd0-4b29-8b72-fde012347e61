using AwhGameServer.Application.UseCases.Authentication;

namespace AwhGameServer.WebApi.Extensions;

/// <summary>
/// Расширения для регистрации сервисов уровня приложения в контейнере зависимостей.
/// </summary>
public static class ApplicationServiceCollectionExtensions
{
    /// <summary>
    /// Регистрирует сервисы уровня приложения в контейнере зависимостей.
    /// </summary>
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        services.AddScoped<LoginCommandHandler>();
        services.AddScoped<RefreshCommandHandler>();
        
        return services;
    }
}
