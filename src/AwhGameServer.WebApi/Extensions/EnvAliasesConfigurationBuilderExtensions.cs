namespace AwhGameServer.WebApi.Extensions;

public static class EnvAliasesConfigurationBuilderExtensions
{
    /// <summary>
    /// Загружает alias'ы ENV→ConfigPath из json-файла и добавляет их как InMemory конфиг.
    /// </summary>
    /// <param name="builder">Строитель конфигурации.</param>
    /// <param name="path">Путь к json-файлу с алиасами.</param>
    /// <param name="optional">Не кидать исключение, если файл не найден.</param>
    /// <remarks>
    /// Формат envAliases.Json:
    /// <code>
    /// {
    ///   "Aliases": {
    ///     "ENV_NAME": "Section:Key",
    ///     "ANOTHER_ENV": "Other:Section:Key"
    ///   }
    /// }
    /// </code>
    /// </remarks>
    public static IConfigurationBuilder AddEnvironmentVariablesAliasesFromJson(
        this IConfigurationBuilder builder,
        string path = "envAliases.json",
        bool optional = true)
    {
        // читаем файл алиасов отдельным temp-конфигом
        var aliasesConfig = new ConfigurationBuilder()
            .SetBasePath(AppContext.BaseDirectory)
            .AddJsonFile(path, optional: optional, reloadOnChange: false)
            .Build();

        var aliasSection = aliasesConfig.GetSection("Aliases");
        if (!aliasSection.Exists())
            return builder; // файла/секции нет — просто ничего не делаем

        var map = new Dictionary<string, string?>();
        foreach (var kv in aliasSection.GetChildren())
        {
            var envName = kv.Key;              // имя переменной окружения
            var targetConfigPath = kv.Value;   // куда проецировать

            if (string.IsNullOrWhiteSpace(envName) || string.IsNullOrWhiteSpace(targetConfigPath))
                continue;

            var raw = Environment.GetEnvironmentVariable(envName);
            var cleaned = Clean(raw);

            if (!string.IsNullOrWhiteSpace(cleaned))
                map[targetConfigPath] = cleaned;
        }

        if (map.Count > 0)
            builder.AddInMemoryCollection(map!);

        return builder;

        static string? Clean(string? s) =>
            string.IsNullOrWhiteSpace(s) ? null :
                s.Trim().Trim('"', '\'').TrimEnd('\r', '\n');
    }
}
