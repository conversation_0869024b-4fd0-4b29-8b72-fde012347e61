using Microsoft.AspNetCore.Mvc;
using AwhGameServer.Application.UseCases.Authentication;
using AwhGameServer.Contracts.Api.V3.Authentication;
using AwhGameServer.WebApi.Mappings.Contracts.Api.V3.Authentication;

namespace AwhGameServer.WebApi.Controllers.Api.V3;

/// <summary>
/// Контроллер для обработки запросов аутентификации пользователей.
/// Предоставляет API для входа в систему и обновления токенов доступа.
/// </summary>
[ApiController]
[Route("api/v3/authentication")]
public class Authentication : Controller
{
    /// <summary>
    /// Выполняет аутентификацию пользователя на основе предоставленных учетных данных.
    /// </summary>
    /// <param name="request">Запрос на вход, содержащий идентификационные данные пользователя</param>
    /// <param name="authCommandHandler">Обработчик команды входа в систему</param>
    /// <returns>Результат аутентификации с токенами доступа</returns>
    /// <response code="200">Успешная аутентификация</response>
    /// <response code="400">Некорректные данные запроса</response>
    /// <response code="401">Неверные учетные данные</response>
    [HttpPost("login")]
    public async Task<IActionResult> Login(
        [FromBody] LoginRequest request,
        LoginCommandHandler authCommandHandler)
    {
        var loginCommand = request.MapToApplicationCommand();

        var loginCommandResult = await authCommandHandler.Handle(loginCommand, CancellationToken.None);
        
        var response = loginCommandResult.MapToContract();
        
        return Ok(response);
    }
    
    /// <summary>
    /// Обновляет токены доступа пользователя на основе refresh token.
    /// </summary>
    /// <param name="request">Запрос на обновление токенов, содержащий refresh token</param>
    /// <param name="refreshCommandHandler">Обработчик команды обновления токенов</param>
    /// <returns>Новые токены доступа</returns>
    /// <response code="200">Успешное обновление токенов</response>
    /// <response code="400">Некорректные данные запроса</response>
    /// <response code="401">Недействительный refresh token</response>
    [HttpPost("refresh")]
    public async Task<IActionResult> Refresh(
        [FromBody] RefreshRequest request,
        RefreshCommandHandler refreshCommandHandler)
    {
        var refreshCommand = request.MapToApplicationCommand();
        
        var refreshCommandResult = await refreshCommandHandler.Handle(refreshCommand, CancellationToken.None);
        
        var response = refreshCommandResult.MapToContract();
        
        return Ok(response);
    }
}
