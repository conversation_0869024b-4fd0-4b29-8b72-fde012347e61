<!--
Проект AwhGameServer.Contracts

Содержит контракты API для игрового сервера AWH (Pixorbi).
Определяет модели данных и интерфейсы для взаимодействия между клиентом и сервером.

Основные компоненты:
- Api/V3/Authentication - контракты для аутентификации пользователей (вход, обновление токенов)
- Api/V3/Models - базовые модели данных (идентификационные данные, токены доступа)

Архитектурная роль:
Представляет слой контрактов в Clean Architecture, обеспечивая стабильный интерфейс
между веб-API и клиентскими приложениями. Изолирует внешние зависимости от бизнес-логики.

Версионирование:
Поддерживает версионирование API через пространства имен (V3).
При изменении контрактов создавать новые версии для обратной совместимости.
-->

<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

</Project>
